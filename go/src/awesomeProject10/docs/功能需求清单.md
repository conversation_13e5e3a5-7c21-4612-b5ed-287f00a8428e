# 时间序列异常检测系统功能需求清单

> 基于Metis API文档提炼的完整功能清单，用于Go语言重新实现

## 📋 目标分类

### 🎯 一、核心目标（必须实现）

#### 1.1 时间序列异常检测API
- **量值检测API** (`/PredictValue`)
  - 适用于大多数KPI指标数据的检测
  - 使用无监督和有监督联合检测
  - 会加载检测模型（XGBoost等）
  - 支持自定义模型（taskId参数）

- **率值检测API** (`/PredictRate`)
  - 适用于正态分布类型数据的检测
  - 使用无监督算法进行检测
  - 专门用于成功率等生死指标数据的检测

#### 1.2 数据处理核心功能
- **时间窗口处理**
  - 固定时间窗口：3小时窗口（180个数据点，每分钟1个点）
  - 当前数据：181个数据点（待检测点+前180个点）
  - 昨日同比数据：361个数据点（前后各180个点）
  - 一周前同比数据：361个数据点（前后各180个点）

- **数据格式支持**
  - 量值数据：整数类型（int）
  - 率值数据：浮点数类型（double）
  - 数据格式：逗号分隔的字符串格式

#### 1.3 C库集成
- **CGO封装**：封装libdetect.so中的核心函数
  - `load_model()` - 加载XGBoost模型
  - `value_predict()` - 量值检测
  - `rate_predict()` - 率值检测
- **数据类型转换**：Go类型与C类型之间的安全转换
- **内存管理**：处理C库的内存分配和释放

#### 1.4 检测结果处理
- **异常判断**
  - 二分类结果：0=异常，1=正常
  - 概率值：返回异常概率值
  - 阈值判断：p<0.15判定为异常（量值检测）
  - 置信度：概率值越小，异常置信度越高

- **返回格式**
  - 标准响应：code、msg、data结构
  - 错误处理：统一的错误码和错误消息
  - 成功标识：0表示成功，非0表示失败

### 🚀 二、扩展目标（优先级较高）

#### 2.1 Prometheus集成
- **数据源集成**：从Prometheus查询时间序列数据
- **PromQL支持**：构建查询语句获取历史数据
- **时间对齐**：处理时区、时间窗口对齐等问题

#### 2.2 配置管理
- **配置文件支持**：YAML/JSON配置文件
- **环境变量**：支持通过环境变量覆盖配置
- **参数管理**：
  - Prometheus服务器地址和认证信息
  - XGBoost模型文件路径
  - API服务器端口和超时设置
  - 检测阈值和窗口参数

#### 2.3 错误处理和监控
- **错误分类**：定义不同类型的错误（网络、数据、算法等）
- **错误恢复**：实现重试机制和降级策略
- **日志系统**：结构化日志记录，支持不同日志级别
- **监控指标**：暴露Prometheus格式的监控指标

### 🔧 三、高级目标（可选实现）

#### 3.1 算法扩展
- **特征提取功能**
  - 统计特征：基础统计量计算
  - 拟合特征：时间序列拟合特征
  - 分类特征：分类相关特征
  - 特征维度：90+维特征
  - 自定义特征：支持增加自定义特征

- **机器学习算法**
  - 统计判别算法：传统统计方法
  - 指数移动平均算法：EMA算法
  - 多项式算法：多项式拟合
  - GBDT算法：梯度提升决策树
  - XGBoost算法：极端梯度提升

#### 3.2 模型管理
- **模型加载**：支持加载预训练模型
- **默认模型**：系统提供默认检测模型
- **自定义模型**：支持用户自定义模型训练
- **模型句柄管理**：C库模型句柄管理
- **模型版本控制**：模型的管理和版本控制

#### 3.3 系统架构扩展
- **分层架构**
  - 数据层：存储检测异常信息、样本信息、任务信息
  - 服务层：数据驱动模块(DAO) + 业务模块(service)
  - 学件层：检测模块 + 特征计算模块 + 算法模块
  - 接口层：API能力提供
  - WEB层：WEB管理界面

- **扩展性功能**
  - 可重用：学件具有可重用特性
  - 可演进：支持模型演进和更新
  - 可了解：提供模型解释能力

### 🎨 四、未来目标（长期规划）

#### 4.1 WEB管理界面
- **异常查询**：异常数据的分页查询、检索、放缩
- **样本管理**：检索、图示、编辑、删除、导入功能
- **标注打标**：标记/取消标记为正负样本
- **样本转存**：标记后样本自动转存样本库

#### 4.2 多数据源支持
- **数据源扩展**：除Prometheus外，支持其他数据源
  - InfluxDB
  - CloudWatch
  - 自定义数据源
- **多数据源聚合**：支持多个数据源的数据聚合

#### 4.3 高级分析功能
- **批量检测**：支持多个指标同时检测
- **检测流水线**：多个检测器串联
- **结果后处理**：降噪、聚合等
- **告警集成**：与告警系统集成

## 📊 应用场景

### 监控数据类型
- **操作系统数据**：CPU、内存、磁盘、流量、包量等
- **应用程序数据**：读写量、调用量、自定义监控指标等
- **KPI指标数据**：交易量、收入值、在线数、成功率、失败量等

### 运维场景
- **异常检测**：实时异常检测和告警
- **监控告警**：运维监控告警领域应用
- **智能运维**：AI运维组件能力

## 📝 实施优先级

### Phase 1: 核心功能（必须完成）
1. C库集成和CGO封装
2. 基础的量值检测和率值检测API
3. 基本的数据格式处理
4. 简单的HTTP API服务器

### Phase 2: 扩展功能（重要）
1. Prometheus数据源集成
2. 配置管理系统
3. 错误处理和日志系统
4. 基础测试和文档

### Phase 3: 高级功能（可选）
1. 特征提取和算法扩展
2. 模型管理功能
3. 系统架构优化
4. 性能优化

### Phase 4: 未来功能（长期）
1. WEB管理界面
2. 多数据源支持
3. 高级分析功能
4. 企业级特性

---

> **更新时间：** 2025-08-04  
> **文档版本：** v1.0  
> **基于：** Metis API文档分析
