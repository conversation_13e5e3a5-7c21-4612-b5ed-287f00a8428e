# 时间序列异常检测系统功能需求清单

> 基于Metis API文档提炼的完整功能清单，用于Go语言重新实现

## 📋 目标分类

### 🎯 一、核心目标（必须实现）

#### 1.1 时间序列异常检测API
- **量值检测API** (`/PredictValue`)
  - 适用于大多数KPI指标数据的检测
  - 使用无监督和有监督联合检测
  - 会加载检测模型（XGBoost等）
  - 支持自定义模型（taskId参数）

- **率值检测API** (`/PredictRate`)
  - 适用于正态分布类型数据的检测
  - 使用无监督算法进行检测
  - 专门用于成功率等生死指标数据的检测

#### 1.2 数据处理核心功能
- **时间窗口处理**
  - 固定时间窗口：3小时窗口（180个数据点，每分钟1个点）
  - 当前数据：181个数据点（待检测点+前180个点）
  - 昨日同比数据：361个数据点（前后各180个点）
  - 一周前同比数据：361个数据点（前后各180个点）

- **数据格式支持**
  - 量值数据：整数类型（int）
  - 率值数据：浮点数类型（double）
  - 数据格式：逗号分隔的字符串格式

#### 1.3 C库集成
- **CGO封装**：封装libdetect.so中的核心函数
  - `load_model()` - 加载XGBoost模型
  - `value_predict()` - 量值检测
  - `rate_predict()` - 率值检测
- **数据类型转换**：Go类型与C类型之间的安全转换
- **内存管理**：处理C库的内存分配和释放

#### 1.4 检测结果处理
- **异常判断**
  - 二分类结果：0=异常，1=正常
  - 概率值：返回异常概率值
  - 阈值判断：p<0.15判定为异常（量值检测）
  - 置信度：概率值越小，异常置信度越高

- **返回格式**
  - 标准响应：code、msg、data结构
  - 错误处理：统一的错误码和错误消息
  - 成功标识：0表示成功，非0表示失败


